﻿using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using Avalonia.Diagnostics;
using PropertyChanged;
using ReactiveUI;
using System.ComponentModel;
using Avalonia.Data.Converters;
using System;
using System.Globalization;
using Avalonia.Interactivity;
using Avalonia.Media.Imaging;
using System.IO;
using Tabula.PMCore;
using Newtonsoft.Json;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Reflection;
using Avalonia.Input;
using Avalonia.Platform;
using Avalonia.Media;
using System.Threading;
using Avalonia.Controls.Primitives;
using MsBox.Avalonia.Enums;
using MsBox.Avalonia;
using Tabula.SharedObjectMap;
using Avalonia.Threading;
using System.Collections;

namespace Tabula.PWGClient
{
    [DoNotNotify]
    public partial class MainView : UserControl
    {
        public static MainView Instance;

        public static bool KeyboardPanPressed = false;

        public List<SKSceneUserControl> SceneControls = new List<SKSceneUserControl>();
        public SKSceneUserControl VisibleSceneControl = null;

        public MainView()
        {
            Instance = this;

            InitializeComponent();
        }

		protected override void OnInitialized()
		{
			base.OnInitialized();

			DataContext = MainWindowUI.Instance;

			SceneControls.Add(LevelEditor.Instance);
			SceneControls.Add(ScreenEditor.Instance);

			// NOTE: New way to catch keys globally
			// https://github.com/AvaloniaUI/Avalonia/discussions/12179
			InputElement.KeyDownEvent.AddClassHandler<TopLevel>(OnKeyDown, handledEventsToo: true);
			InputElement.KeyDownEvent.AddClassHandler<TopLevel>(OnKeyUp, handledEventsToo: true);


            // SukiUI theme
			//SukiUI.ColorTheme.LoadDarkTheme(Application.Current);

			// initialize storageprovider  (needed for Android)
			FileSystem.Configure(this);

            // initialize async to start earlier
            Task.Run(() => App.Instance.OnStartup());

            // initialize check ui task in dispatcher
            DispatcherTimer check_ui_timer = new DispatcherTimer(TimeSpan.FromSeconds(0.1), DispatcherPriority.Normal, (s, e) => CheckUI());

			// First Scene selected
			tabcontrol_selectionchanged(null, null);
		}

        private void CheckUI()
        {
            if (MainWindowUI.Instance == null)
                return;
            if (!MainWindowUI.Instance.IsConnected)
                return;

            if (MainWindowUI.Instance.MainSelectedItem != MainWindowUI.Instance.SelectedItem)
            {
                MainWindowUI.Instance.SelectedItem = null;
                MainWindowUI.Instance.SelectedItem = MainWindowUI.Instance.MainSelectedItem;
            }
		}

        private void OnKeyDown(object s, KeyEventArgs e)
        {
			RouteKeyToFirstVisibleScene(true, e);

			//if (e.Key == Key.A)  KeyboardPanPressed = true;
			// KeyboardPanPressed = e.KeyModifiers.HasFlag(KeyModifiers.Control);
		}

		private async void OnKeyUp(object s, KeyEventArgs e)
		{
			RouteKeyToFirstVisibleScene(false, e);

			// if (e.Key == Key.A) KeyboardPanPressed = false;

			// KeyboardPanPressed = e.KeyModifiers.HasFlag(KeyModifiers.Control);


            // Special keys for tests (avoid using buttons etc..)
#if DEBUG
            /*
            if (e.Key == Key.F1)
			{
				await ProgressDialog.ShowMessageAsync($"Testing dialog: {DateTime.Now.ToLongTimeString()}");
			}
            else if (e.Key == Key.F2)
            {
				var box = MessageBoxManager.GetMessageBoxStandard("Caption", "Are you sure you would like to delete appender_replace_page_1?",ButtonEnum.YesNo);

                var result = await box.ShowAsPopupAsync(this);
			}
            */
#endif
        }

		// LICENSE CHECK VALUES:
		// -100: cannot load license or no license
		// -101: exception loading license (filesystem?)
		// -102: exception checking license (filesystem?)

		// LocalAppConfig.CheckLicense()
		// 1: ok, 
		//-1: failed for HC or exception
		//-2: failed for date expired           (STRANGELY NOT HAPPENING WHEN DATE LICENSE EXPIRES)
		//-3: failed for instances lock
		//-4: failed for uses lock
		//-5: failed for everlasting license
		//-6: failed for invalid DEV or non loaded
		//-7: failed for blacklisted hid or serial
		//-8: failed for unknown expired license (EXPIRED DATE LICENSE, SUBSCRIPTION)

		public async Task<bool> AskLicenseActivation(bool app_startup = false)
        {
			// First check for trial and licensing

			if (App.IsTrialExpired)
            {
				// TODO:                
				await ProgressDialog.ShowMessageAsync($"Your S-ARGAME Trial is expired!\n\nIt will run for a shorter time.", show_logo: true);

				return true;
			}
            else if (App.IsTrial)
            {
                if (app_startup)
                {
					// WORKAROUND: it seems trial on Mac do not work... they give 0 as current day?
                    if (App.LicenseInfo.getTrialDay() == 0)
						await ProgressDialog.ShowMessageAsync($"Welcome to S-ARGAME Trial", show_logo: true);
                    else
					    await ProgressDialog.ShowMessageAsync($"Welcome to S-ARGAME Trial\n\nYou are on day {App.LicenseInfo.getTrialDay()} of {App.LicenseInfo.getTrialDays()}", show_logo: true);
                }

				return true;
			}
            else if (!App.IsLicensed)
            {
                // special message
                string message = "";

                switch (App.LicenseCheck)
                {
                    case -2:
                    case -8:
						message = $"License has expired.";
						break;

                    case -100:
                        message = "No license found.";
                        break;

					default:
                        message = $"Cannot load license (error: {App.LicenseCheck})";
                        break;
                }

                message += "\n\nPlease activate a new license or Trial.";

#if DEBUG
				message += $"\n\nIsTrial={App.IsTrial} IsTrialExpired={App.IsTrialExpired} license_ret: {App.LicenseCheck}";
#endif


				await ProgressDialog.ShowMessageAsync(message, show_logo: true);

                tab_dashboard.IsSelected = true;
                var tl = page_dashboard.FindControl<TabItem>("tab_license");
                tl.IsSelected = true;

                return false;
            }
            else
                return true;
		}

		#region INotifyPropertyChanged

		protected void RaisePropertyChanged(PropertyChangedEventArgs args)
        {
            ((IReactiveObject)this).RaisePropertyChanged(args);
        }

        protected void RaisePropertyChanged(string name)
        {
            ((IReactiveObject)this).RaisePropertyChanged(name);
        }

        #endregion


        #region UI Events

        // NOTE: it seems this is raised also for child TabControls
        async void tabcontrol_selectionchanged(object sender, SelectionChangedEventArgs args)
		{
            if (maintab == null)
                return;

            //if (args!=null && args.AddedItems!=null && args.AddedItems.Count>0 && args.AddedItems[0]!=null)
            {
                if (args == null)
                {
                    // Initialization only
					MainWindowUI.Instance.IsInspectorVisible = false;
                    MainWindowUI.Instance.MainSelectedItem = null;
					LevelEditor.Instance?.Scene.UnSelectAllObjects();
					ScreenEditor.Instance?.Scene.UnSelectAllObjects();
				}
                else if (args.AddedItems.Count ==0)
                {
                    // ?
                }
                else if (args.AddedItems[0] == tab_dashboard || args.AddedItems[0] == tab_projection)
                {
                    MainWindowUI.Instance.IsInspectorVisible = true;
					MainWindowUI.Instance.MainSelectedItem = null;
					LevelEditor.Instance?.Scene.UnSelectAllObjects();
					ScreenEditor.Instance?.Scene.UnSelectAllObjects();
				}
                else if (args.AddedItems[0] == tab_level_design)
				{
                    MainWindowUI.Instance.IsInspectorVisible = true;
					MainWindowUI.Instance.MainSelectedItem = null;
					LevelEditor.Instance?.Scene.UnSelectAllObjects();
					ScreenEditor.Instance?.Scene.UnSelectAllObjects();
				}
				else if (args.AddedItems[0] == tab_game_settings)
				{
					MainWindowUI.Instance.IsInspectorVisible = false;
					MainWindowUI.Instance.MainSelectedItem = null;
					LevelEditor.Instance?.Scene.UnSelectAllObjects();
					ScreenEditor.Instance?.Scene.UnSelectAllObjects();
				}
				else
                {
                    // FIXME: probably raised by some other tabs..
                    return;
                }

                //MainWindowUI.Instance.RaisePropertyChanged(nameof(MainWindowUI.Instance.IsInspectorVisible));
			}

            // Events
            if (args!=null && maintab!=null)
            {
                if (args.RemovedItems != null)
                    foreach (var c in args.RemovedItems)
                    {
                        var tabitem = c is TabItem ? (c as TabItem).Content as ITabbedPage : null;
                        tabitem?.OnTabUnselected();
                    }

                if (args.AddedItems != null)
                    foreach (var c in args.AddedItems)
					{
						var tabitem = c is TabItem ? (c as TabItem).Content as ITabbedPage : null;
						tabitem?.OnTabSelected();
					}
			}

            var scene_control = GetFirstVisibleScene();
            if (scene_control != null)
            {
                if (!scene_control.Equals(VisibleSceneControl))
                    if (VisibleSceneControl!=null)
                        VisibleSceneControl.OnBecameInvisible();

                VisibleSceneControl = scene_control;
                if (VisibleSceneControl!=null)
                    VisibleSceneControl?.OnBecameVisible();
            }
            else
                if (VisibleSceneControl!=null)
                    VisibleSceneControl?.OnBecameInvisible();
		}

		async void joincode_PointerPressed(object sender, PointerPressedEventArgs e)
		{
            tab_dashboard.IsSelected = true;
            page_dashboard.tab_tools.IsSelected = true;
		}

		// TODO: define preprocessor directives in build
		#region Focus on Win32

		[System.Runtime.InteropServices.DllImport("user32.dll")]
		private static extern bool SetForegroundWindow(IntPtr hWnd);

		[System.Runtime.InteropServices.DllImport("user32.dll")]
		private static extern IntPtr SetFocus(IntPtr hWnd);

		[System.Runtime.InteropServices.DllImport("user32.dll")]
		private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

		#endregion

		async void bt_runningmodule_thumb_Click(object sender, RoutedEventArgs args)
        {
            if (MainWindowUI.Instance.IsConnected)
            {
                // Only for Windows now
                if (OperatingSystem.IsWindows())
                {
                    try
                    {
                        string hwnd_string = await App.Client.Client.GetWindowHandle();
                        IntPtr hwnd = new IntPtr(Convert.ToInt64(hwnd_string));

                        SetForegroundWindow(hwnd);
                        SetFocus(hwnd);

                        // do not restore if we are in spout mode
                        if (!MainWindowUI.Instance.CustomResolutionSpout)
                            ShowWindow(hwnd, 10);
                    }
                    catch { }
                }
            }

            // Select game info tab
            /*
            if (MainWindowUI.Instance.IsModuleProcessRunningAndConnected)
            {
				MainView.Instance.tab_game_settings.IsSelected = true;
			}
            */
		}

		async void bt_connect_server_Click(object sender, RoutedEventArgs args)
        {
			try
            {
                if (!App.IsConnected)
                {
                    var cts = new CancellationTokenSource();

					var msgbox = ProgressDialog.ShowMessage(
                        message: "Connecting...",
						cancel_button_text: "Cancel",
                        on_cancel_button_click: () => cts.Cancel(),
                        progress: true);

					var ret = await App.Instance.ConnectToServer(main_timeout_token: cts.Token);

                    if (ret <= 0)
                    {
                        msgbox.Close();

                        string err = "";
                        switch(ret)
                        {
                            case -30: err = "Protocol mismatch. Update or use a compatible Editor version"; break;
                        }

                        App.Local.log("Cannot connect to server: "+err);

                        if (!cts.IsCancellationRequested)
                            await ProgressDialog.ShowMessageAsync($"Cannot connect to server.\n{err}\nPlease retry.");

						await App.Instance.DisconnectFromServer();

						return;
                    }

                    msgbox.Close();

                    DataContext = MainWindowUI.Instance;
                    MainWindowUI.Instance.RaiseAllPropertyChanged();

				    MainView.Instance.tab_level_design.IsSelected = true;

					await App.Instance.CheckProtocolVersion();
				}
                else
                {
					var choice = await ProgressDialog.ShowChoicesAsync("Disconnect ?", new string[] { "Yes", "No" });

					if (choice == ProgressDialog.Choice.Choice2 || choice == ProgressDialog.Choice.None)
						return;

					MainWindowUI.Instance.IsInspectorVisible = false;
					MainWindowUI.Instance.MainSelectedItem = null;

					var msgbox = ProgressDialog.ShowMessage(
						message: "Disconnecting...",
						progress: true);

					await App.Instance.DisconnectFromServer();

                    msgbox.Close();
				}
            }
            catch(Exception ex)
            {
				ToggleSceneControlsRendering(true);

				Tabula.Log.Logger.DefaultLog.logException("Connect", ex);
#if DEBUG
                throw ex;
#endif

				
				//App.MessageBox($"EXCEPTION: {ex} {ex.Message}");                
            }

            ToggleSceneControlsRendering(true);
        }

        private async Task<Model> LoadModelFromFile()
        {                       
            var filename = await OpenDialog(
                    "Load Project",
                _last_folder,
                "",
                "JSON files", new List<string>() { "json" });

            if (File.Exists(filename))
            {
                Model model = null;
                try
                {
                    model = JsonConvert.DeserializeObject<Model>(File.ReadAllText(filename));
                  
                    _last_folder = new FileInfo(filename).DirectoryName;

                    return model;
                }
                catch (Exception ex)
                {
                    //App.MessageBox($"EXCEPTION: {ex} {ex.Message}");
                    throw ex;
                }
            }

            return null;                        
        }

        /*
        private async Task<bool> UploadModelToPlayer(Model model, bool wall_only = false)
		{
            try
            {
                ToggleSceneControlsRendering(false);

                if (!App.Client.IsConnected)
                {
                    var ci = await App.Instance.ConnectToServer();
                    if (ci == null)
                        throw new Exception("Cannot Connect");
                }

                await App.Client.View.LoadProject(model, wall_only);

                await App.Instance.DisconnectFromServer();

                await Task.Delay(500);

                await App.Instance.ConnectToServer();

                ToggleSceneControlsRendering(true);

                return true;
            }
            catch (Exception ex)
            {
                LevelEditor.Instance.RenderScene = true;
                ScreenEditor.Instance.RenderScene = true;
            }

            return false;
        }
        */

		async void bt_stop_module_Click(object sender, RoutedEventArgs args)
		{
			var choice = await ProgressDialog.ShowChoicesAsync("Stop the application?\nMake sure you saved your changes.", new string[] { "Yes", "No" });

			if (choice == ProgressDialog.Choice.Choice2 || choice == ProgressDialog.Choice.None)
				return;

            await App.Instance.DisconnectAndStopModule();
		}

		async void bt_reset_module_Click(object sender, RoutedEventArgs args)
		{
			var choice = await ProgressDialog.ShowChoicesAsync("Reset the game?", new string[] { "Yes", "No" });

			if (choice == ProgressDialog.Choice.Choice2 || choice == ProgressDialog.Choice.None)
				return;

			App.Client?.View?.CallMethod("ResetGame", new object[] { });
		}


		string _last_folder = null;

        /*
        async void bt_load_project_Click(object sender, RoutedEventArgs args)
        {
            var model = await LoadModelFromFile();

            await UploadModelToPlayer(model);
        }

        async void bt_load_wall_Click(object sender, RoutedEventArgs args)
        {
            var model = await LoadModelFromFile();

            await UploadModelToPlayer(model, true);
        }

        async void bt_load_scene_Click(object sender, RoutedEventArgs args)
		{
            var scenes = await MainWindowUI.Instance.SceneNames;
            int scene_index = scenes.IndexOf(MainWindowUI.Instance.SelectedSceneName);
            await App.Client.Client.LoadScene(scene_index);
        }
        */

        async void bt_save_project_Click(object sender, RoutedEventArgs args)
        {
            if (!App.IsConnected)
                return;

            MainWindowUI.Instance.IsBusy = true;

            await MainWindowUI.Instance.View.SaveModel();

            await Task.Delay(2000);

			MainWindowUI.Instance.IsBusy = false;
		}

		// Receives the project files and saves it locally
		async void bt_download_project_Click(object sender, RoutedEventArgs args)
		{
            try
            {
                var project = await App.Client.DownloadModel();
                if (project == null)
                    return;

                var json = JsonConvert.SerializeObject(project);
                json = SharedObjectMap.SharedObjectMap.CleanJsonFromGuids(json);


                string filename = await SaveDialog(
                    "Save Project", 
                    _last_folder, 
                    $"{project.ProjectName}.json", 
                    "JSON files", new List<string>() { "json" });

                if (!string.IsNullOrEmpty(filename))
                    File.WriteAllText(filename, json);

                _last_folder = new FileInfo(filename).DirectoryName;
            }
            catch
			{

			}
		}

        // This happens only interactively?
		async void hierarchy_SelectionChanged(object sender, SelectionChangedEventArgs args)
		{
            object item_added = args.AddedItems != null && args.AddedItems.Count > 0 ? args.AddedItems[0] : null;
            //object item_removed = args.RemovedItems != null && args.RemovedItems.Count > 0 ? args.RemovedItems[0] : null;

            // System.Diagnostics.Debug.WriteLine($"***** SELECTION CHANGED added:{args.AddedItems.Count} deleted:{args.RemovedItems.Count}****");

            if (item_added == null)
                return;


            if (item_added is StructureGroup)
            {
				var structure_group = (StructureGroup) item_added;
				if (structure_group.HasItems)
				{
					// Open and unselect
					structure_group.IsExpanded = !structure_group.IsExpanded;

					MainWindowUI.Instance.ForceMainSelectedItem(null);
					return;
				}					
			}


            if (item_added is EntityView)
            {
                var entity_view = (EntityView)item_added;
                if (entity_view.HasItems)
                {
                    // Open and unselect
                    entity_view.IsExpanded = !entity_view.IsExpanded;

					MainWindowUI.Instance.ForceMainSelectedItem(null);
					return;
				}
					
                   
            }


            MainWindowUI.Instance.MainSelectedItem = item_added;

        }


		// Next background
		int current_background_index = -1;
        async void bt_next_background_Click(object sender, RoutedEventArgs args)
        {
            try
            {
                // Get background count
                int bgcount = Convert.ToInt32(await App.Client.View.GetProperty("BackgroundCount"));

                current_background_index = (current_background_index + 1) % bgcount;

                await App.Client.View.CallMethod("SetBackground", new object[] { current_background_index });
            }
            catch
            {

            }
        }

		#region Hierarchy Items

		void bt_flyout(object sender, RoutedEventArgs args)
		{
			FlyoutBase.ShowAttachedFlyout(sender as Control);
		}

		#endregion

		// Calibration
		async void bt_calibrate_Click(object sender, RoutedEventArgs args)
        {
            // Open calibration window
            var win = new RoomCalibrationWindow();
            await win.ShowDialog(MainWindow.Instance);
        }

        // Next Scene
        private int scene_index=0;
        async void bt_next_scene_Click(object sender, RoutedEventArgs args)
        {
            var scenes = await App.Client.Client.GetScenes();
            var current_scene_index = await App.Client.Client.GetCurrentSceneIndex();

            scene_index = (current_scene_index + 1) % scenes.Count;
            await App.Client.Client.LoadScene(scene_index);
        }



        public async Task<string> OpenDialog(string title, string workdir, string filename, string filter_name = "All Files", List<string> filter_extensions = null)
        {
            OpenFileDialog OpenFileBox = new OpenFileDialog();
            OpenFileBox.AllowMultiple = false;
            OpenFileBox.Title = title;
            OpenFileBox.InitialFileName = filename;
            OpenFileBox.Directory = (workdir != null ? workdir : Assembly.GetExecutingAssembly().Location);
            List<FileDialogFilter> Filters = new List<FileDialogFilter>();
            FileDialogFilter filter = new FileDialogFilter();
            if (filter_extensions != null)
            {
                filter.Extensions = filter_extensions;
                filter.Name = filter_name;
                Filters.Add(filter);
                OpenFileBox.Filters = Filters;
            }

            // SaveFileBox.DefaultExtension = "doc";

            var files = await OpenFileBox.ShowAsync(MainWindow.Instance);
            if (files != null && files.Length > 0)
                return files[0];
            else
                return null;
        }

        public async Task<string> SaveDialog(string title, string workdir, string filename, string filter_name="All Files", List<string> filter_extensions=null)
		{
            SaveFileDialog SaveFileBox = new SaveFileDialog();
            SaveFileBox.Title = title;
            SaveFileBox.InitialFileName = filename;
            SaveFileBox.Directory = (workdir != null ? workdir : Assembly.GetExecutingAssembly().Location);
            List<FileDialogFilter> Filters = new List<FileDialogFilter>();
            FileDialogFilter filter = new FileDialogFilter();
            if (filter_extensions != null)
            {
                filter.Extensions = filter_extensions;
                filter.Name = filter_name;
                Filters.Add(filter);
                SaveFileBox.Filters = Filters;
            }

            // SaveFileBox.DefaultExtension = "doc";

            return await SaveFileBox.ShowAsync(MainWindow.Instance);
        }

#endregion


		#region Scene Controls

        public void ToggleSceneControlsRendering(bool toggle)
		{
            // TODO: if false, should wait for end of last render

            foreach (var c in SceneControls)
                c.RenderScene = toggle;
		}

        public SKSceneUserControl GetFirstVisibleScene()
		{
            foreach (var c in SceneControls)
                if (c.IsSceneVisible)
                    return c;

            return null;
        }

        public void RouteKeyToFirstVisibleScene(bool is_down, KeyEventArgs args)
        {
            var scene = GetFirstVisibleScene();
            scene?.HandleKey(is_down, args);
        }

		#endregion
	}


	// Test special converters

	public class DebugConverter : IValueConverter
	{
		public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
		{
            return value;
		}

		public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
		{
            return value;
		}
	}

	public class StringCapitalizeConverter : IValueConverter
	{
		public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
		{
			string s = System.Convert.ToString(value);

            return s.ToUpper();
		}

		public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
		{
            return value;
		}
	}

	public class StringToBoolConverter : IValueConverter
	{
		public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
		{
			string s = System.Convert.ToString(value);

			return (!string.IsNullOrEmpty(s) ? true : false);
		}

		public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
		{
            throw new NotImplementedException();
		}
	}

	public class BoolToOpacityConverter : IValueConverter
	{
		public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
		{
            bool b = System.Convert.ToBoolean(value);

			return (b ? 1.0 : 0.0);
		}

		public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
		{
			double d = System.Convert.ToDouble(value);

            return (d == 0.0 ? false : true);

			return value;
		}
	}

	public class CollectionHasElementsConverter : IValueConverter
	{
		public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
		{
            var i = value as IList;

            return i!=null && i.Count > 0;
		}

		public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
		{
			return AvaloniaProperty.UnsetValue;
		}
	}

	public class IntConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            int d = (int) System.Convert.ToInt32(value);
            string s = (d == 0 ? "0" : d.ToString());

            return s;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            string s = value.ToString();
            int d;
            if (int.TryParse(s, System.Globalization.NumberStyles.AllowDecimalPoint | NumberStyles.AllowLeadingSign, System.Globalization.CultureInfo.InvariantCulture, out d))
                return d;
            else
                return null;
        }
    }    

	// two decimal points 
	public class FloatConverter2 : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            float d = (float) value;
            string s = (d==0 ? "0.00" : d.ToString("#.##", System.Globalization.CultureInfo.InvariantCulture));

            return s;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            string s = value.ToString();
            float d;
            if (float.TryParse(s, System.Globalization.NumberStyles.AllowDecimalPoint | NumberStyles.AllowLeadingSign, System.Globalization.CultureInfo.InvariantCulture, out d))
                return d;
            else
                return null;
        }
    }

    // four decimal points 
    public class FloatConverter4 : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            float d = (float)value;
            string s = (d == 0 ? "0.0000" : d.ToString("#.####", System.Globalization.CultureInfo.InvariantCulture));

            return s;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            string s = value.ToString();
            float d;
            if (float.TryParse(s, System.Globalization.NumberStyles.AllowDecimalPoint | NumberStyles.AllowLeadingSign, System.Globalization.CultureInfo.InvariantCulture, out d))
                return d;
            else
                return null;
        }
    }

	public class DoubleConverter2 : IValueConverter
	{
		public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
		{
			double d = (double )System.Convert.ToDouble(value);
			string s = (d == 0 ? "0.00" : d.ToString("#.##", System.Globalization.CultureInfo.InvariantCulture));

			return s;
		}

		public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
		{
			string s = value.ToString();
			double d;
			if (double.TryParse(s, System.Globalization.NumberStyles.AllowDecimalPoint | NumberStyles.AllowLeadingSign, System.Globalization.CultureInfo.InvariantCulture, out d))
				return d;
			else
				return null;
		}
	}

	public class BoolToColorToggleConverter : IValueConverter
	{
		public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
		{
			if (value is bool toggle)
			{
                if (toggle)
                    return new SolidColorBrush((Color) parameter);
                else
					return new SolidColorBrush(Colors.LightGray);
			}

			return AvaloniaProperty.UnsetValue;
		}

		public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
		{
			throw new NotImplementedException();
		}

	}

	public class HexColorConverterRGBA : IValueConverter
	{
		public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
		{
			if (value is string rgba)
			{
				// Remove the '#' and swap back RGBA to ARGB
				rgba = rgba.Substring(1);
				string argb = rgba.Substring(6) + rgba.Substring(0, 6);
				if (Color.TryParse("#" + argb, out Color result))
				{
					return result;
				}
			}

			return AvaloniaProperty.UnsetValue;
		}

		public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
		{
			if (value is Color color)
			{
				// Swap ARGB to RGBA and keep the '#'
				uint argb_uint = color.ToUInt32();  // avoid getting real color names
				string argb = $"{argb_uint:X8}";
				string rgba = argb.Substring(2) + argb.Substring(0, 2);
				return "#" + rgba;
			}
			return AvaloniaProperty.UnsetValue;

			
		}
	}

	public class HexColorConverterHSV : IValueConverter
	{
		public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
		{
			if (value is string rgba)
			{
				// Remove the '#' and swap back RGBA to ARGB
				rgba = rgba.Substring(1);
				string argb = rgba.Substring(6) + rgba.Substring(0, 6);

				if (Color.TryParse("#" + argb, out Color color))
				{
					// Convert Color to HsvColor
					var hsv = color.ToHsv();
					return hsv;
				}
			}

			return AvaloniaProperty.UnsetValue;
		}

		public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
		{
			if (value is HsvColor hsv)
			{
				// Convert HsvColor to Color
				var color = HsvToColor(hsv.H, hsv.S, hsv.V);

				// Swap ARGB to RGBA and keep the '#'
				string argb = color.ToString().Substring(1);
				string rgba = argb.Substring(2) + argb.Substring(0, 2);
				return "#" + rgba;
			}

			return AvaloniaProperty.UnsetValue;
		}

		private Color HsvToColor(double h, double s, double v)
		{
			var hi = Math.Floor(h / 60.0) % 6;
			var f = h / 60.0 - Math.Floor(h / 60.0);

			v = v * 255;
			var p = (int)(v * (1 - s));
			var q = (int)(v * (1 - f * s));
			var t = (int)(v * (1 - (1 - f) * s));

			switch (hi)
			{
				case 0:
					return Color.FromArgb(255, (byte)v, (byte)t, (byte)p);
				case 1:
					return Color.FromArgb(255, (byte)q, (byte)v, (byte)p);
				case 2:
					return Color.FromArgb(255, (byte)p, (byte)v, (byte)t);
				case 3:
					return Color.FromArgb(255, (byte)p, (byte)q, (byte)v);
				case 4:
					return Color.FromArgb(255, (byte)t, (byte)p, (byte)v);
				default:
					return Color.FromArgb(255, (byte)v, (byte)p, (byte)q);
			}
		}
	}

	// Capitalizes, removes _
	public class LabelConverter : IValueConverter
	{
		public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
		{
            string _camelcase(string v)
            {
				if (string.IsNullOrEmpty(v))
					return "";

				return v.Substring(0, 1).ToUpper() + v.Substring(1).Replace("_", " ");
			}

            // NOTE: also support binding to any field view.
            if (value is IGuidObjectSyncView)
            {
                var model = ((IGuidObjectSyncView)value).GetModel();
                if (model == null)
                    return "<label_converter_error>";

                switch (model)
                {
                    case Field f:
                        return !string.IsNullOrEmpty(f.name_desc) ? f.name_desc : _camelcase(f.name);

                    default:
                        return "<label_converter_unknown>";
                }

            }

            // standard behaviour, binding is to a string

            string v = (string)value;

            return _camelcase(v);
		}

		public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
		{
            return value;
		}
	}

    // Converts a float to a percentage
	public class FloatToPercentageConverter : IValueConverter
	{
		public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
		{
			float v = (float) value;
			// NOTE: Avalonia doesn't support binding to ConverterParameter :( https://github.com/AvaloniaUI/Avalonia/issues/6840
			float percentage_factor = 100f; // (float) parameter;

            return $"{(int) (v * percentage_factor)} %";
		}

		public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
		{
            string s = value.ToString().Replace("%", "").Trim();
			float d;
			if (float.TryParse(s, System.Globalization.NumberStyles.AllowDecimalPoint | NumberStyles.AllowLeadingSign, System.Globalization.CultureInfo.InvariantCulture, out d))
				return d / 100f;
			else
				return null;
		}
	}

	public class BitmapValueConverter : IValueConverter
	{
		public static BitmapValueConverter Instance = new BitmapValueConverter();

		public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
		{
            if (value == null)
                return null;

			if (value is string && targetType == typeof(IImage))
			{
				var uri = new Uri((string)value, UriKind.RelativeOrAbsolute);
				var scheme = uri.IsAbsoluteUri ? uri.Scheme : "file";

				switch (scheme)
				{
					case "file":
                        {
                            var bitmap = new Bitmap((string)value);
                            return bitmap;
                        }

					default:
                        {
                            var asset = AssetLoader.Open(uri);
                            var bitmap = new Bitmap(asset);
                            return bitmap;
                            /*    
                             AvaloniaLocator.Current.GetService<IAssetLoader>();
                            return new Bitmap(assets.Open(uri));
                            */
                        }
				}
			}

			throw new NotSupportedException();
		}

		public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
		{
			throw new NotSupportedException();
		}
	}
}
