﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <!--If you are willing to use Windows/MacOS native APIs you will need to create 3 projects.
    One for Windows with net7.0-windows TFM, one for MacOS with net7.0-macos and one with net7.0 TFM for Linux.-->
    <TargetFramework>net7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <Platforms>AnyCPU;ARM64</Platforms>
    <Configurations>Debug;Release;Beta Release</Configurations>
  </PropertyGroup>

  <PropertyGroup>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <RootNamespace>Tabula.PWGClient</RootNamespace>
    <AssemblyName>S-ARGAME_Editor</AssemblyName>
    <ApplicationIcon>icon_128.ico</ApplicationIcon>
    <AssemblyVersion>2025.6.2.6</AssemblyVersion>
	<FileVersion>2025.6.2.6</FileVersion>
    <PackageIcon>icon_128.png</PackageIcon>
    <Company>TABULA Natural Interfaces Srl</Company>
    <Copyright>TABULA Natural Interfaces Srl</Copyright>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DebugType>none</DebugType>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
	  <DebugSymbols>False</DebugSymbols>
	  <DebugType>none</DebugType>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Beta Release|AnyCPU'">
    <DebugSymbols>False</DebugSymbols>
    <DebugType>none</DebugType>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM64'">
    <DebugType>none</DebugType>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM64'">
    <DebugType>none</DebugType>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Beta Release|ARM64'">
    <DebugType>none</DebugType>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="icon_128.ico" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Avalonia.Desktop" Version="11.0.10" />
    <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
    <PackageReference Condition="'$(Configuration)' == 'Debug'" Include="Avalonia.Diagnostics" Version="11.0.10" />
    <PackageReference Include="Eziriz.Reactor.TrimHelper" Version="6.9.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PWGClient\PWGClient.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="NobleConnect">
      <HintPath>..\..\..\TABULA.SharedModelRPC\SharedModelRPC\Shared\P2P\NobleConnect.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <None Update="icon_128.png">
      <Pack>True</Pack>
      <PackagePath>\</PackagePath>
    </None>
    <None Update="_sargame\store_mac.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\store_win.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_modulescache\bitbomber.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_modulescache\bubblefi.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_modulescache\calibrator.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_modulescache\footwall.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_modulescache\halloweenxp1.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_modulescache\holidayxp1.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_modulescache\kinetikplayground.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_modulescache\neonraiders.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_modulescache\ponkanoid.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_modulescache\pumpshoot.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_modulescache\roomraiders.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_modulescache\snakepit.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_modulescache\snowballfrenzy.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_modulescache\superpop.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_modulescache\tablerace.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_modulescache\walleroids.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_packagescache\80spack.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_packagescache\halloweenxp.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_packagescache\holidayxp.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_packagescache\kinetikpack.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_packagescache\starterpack.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_scenarios\defaultscenario\scenario.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="_sargame\_scenarios\defaultscenario\scenario.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>
