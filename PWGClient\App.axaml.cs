﻿global using File = Tabula.PWGClient.FileSystem;

using Avalonia;
using Avalonia.Controls;
using Avalonia.Threading;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Markup.Xaml;
using PropertyChanged;
using ReactiveUI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Threading.Tasks;
using Tabula.Log;
using Tabula.SharedObjectMap;
using Tabula.PMCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.Collections.Concurrent;
using Avalonia.Interactivity;
using System.IO;
using SkiaSharp;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using Avalonia.Platform.Storage;
using System.Text.RegularExpressions;
using Tabula.P2P;
using static Tabula.P2P.P2P;
using Tabula.Licensing.LicenseActivator;
using NetSparkleUpdater;
using NetSparkleUpdater.Enums;
using NetSparkleUpdater.SignatureVerifiers;
using System.Reflection;
using Tabula.PWG.SARGAME;
using System.Threading;
using Sentry;
using Tabula.Licensing;
using System.Text;
using Avalonia.Controls.Notifications;
using Org.BouncyCastle.Asn1.X509;
using Tabula.SKRenderGraph;
using Avalonia.Media;
using System.Collections.ObjectModel;

namespace Tabula.PWGClient
{

	[Serializable]
	public class Config
	{
		public string	GameFolder = "";
		public string	ServerAddress = "";				// this is the IP to connect to (so the server)
		public bool		UseP2P = false;
	}


	public class App : Application
	{
		private string AppCastUrl = null;   // will be set in initialization according to OS

		// NOTE: this was an attempt to overcome netsparkle not showing UI on Mac, turned out it was a misconfiguration
		/*
		private string			UpdateDownloadLink = null;
		private UpdateStatus	UpdateCheckStatus = UpdateStatus.UpdateNotAvailable;
		*/

		public static SharedObjectMap_Client<Model, ModelView, PMCore_Client> Client;

		public static Window MainWindow;
		public static MainView MainView;

		public static Config Config;

		public static LocalAppConfig Local;
		internal static SARGAME		 SARGAME { get; set; }
		internal static ReactorLicenseInfo	LicenseInfo { get; private set; }

		// Views
		public static MainWindowUI MainWindowUI = new MainWindowUI();

		// Icon cache
		public static ConcurrentDictionary<string, byte[]> Icons = new ConcurrentDictionary<string, byte[]>();

		

		public static App Instance;

		internal static bool IsLicensed { get; private set; }		// true also for trial
		internal static int	 LicenseCheck { get; private set; }     // the last license check return value

		internal static bool IsTimeLicense{ get; private set; }
		internal static bool IsTimeLicenseExpired { get; private set; }

		internal static bool IsTrial { get; private set; }
		internal static bool IsTrialExpired { get; private set; }

		internal static bool IsConnected => MainWindowUI.IsConnected;

		private string calibration_image_time_path = "calibration_image.json";

		// Special options, exclusions, conditional build
		internal static bool CalibrationEnabled = true;

		// Sparkle Updater
		private SparkleUpdater? _sparkle;

		public override void Initialize()
		{

#if DEBUG
			CalibrationEnabled = true;
			SKScene.Debug = false;
			SharedObjectMap.SharedObjectMap.Debug = false;
#endif

			// appcast update urls
			if (OperatingSystem.IsWindows())
			{
				// TODO: create real beta/dev channels
#if DEBUG
				// AppCastUrl = "https://sargame-cdn.tabulatouch.com/win_x64/editor/appcast_dev.xml";
				AppCastUrl = "https://sargame-cdn.tabulatouch.com/win_x64/editor/appcast.xml";
#else
				AppCastUrl = "https://sargame-cdn.tabulatouch.com/win_x64/editor/appcast.xml";
#endif
			}
			else if (OperatingSystem.IsMacOS())
			{
#if DEBUG
				// AppCastUrl = "https://sargame-cdn.tabulatouch.com/macos_x64/editor/appcast_dev.xml";
				AppCastUrl = "https://sargame-cdn.tabulatouch.com/macos_x64/editor/appcast.xml";
#else
				AppCastUrl = "https://sargame-cdn.tabulatouch.com/macos_x64/editor/appcast.xml";
#endif
			}

			Instance = this;
			AvaloniaXamlLoader.Load(this);
		}

		//RETURN VALUES
		//	1: ok
		//	-100: failed to create LocalAppConfig (read only filesystem?)
		//  -102: cannot find any root folder position
		//  -101: failed SARGAME.Load()

		internal int InitializeLocalAppAndLicense()
		{
			try
			{
				Local = new LocalAppConfig();

				//NOTE: Check for write access to the special folder LocalAppConfig will use as root and if it doesn't work, use another alternative one
				var local_ret = Local.Create("sargame", "Editor");

				// -100: wrong or missing product/module names
				// -101: cannot find suitable root folder

				switch (local_ret)
				{
					case 1: break; // OK

					case -100:
						SentrySdk.CaptureMessage($"LocalAppConfig() creation exception, ret={local_ret}", SentryLevel.Error);
						return -100;

					case -101:
						SentrySdk.CaptureMessage($"LocalAppConfig() creation exception, ret={local_ret}", SentryLevel.Error);
						return -102;
				}
			}
			catch(Exception ex)
			{
				// it can fail for read-only filesystem
				SentrySdk.CaptureMessage($"LocalAppConfig() creation exception", SentryLevel.Error);
				SentrySdk.CaptureException(ex);
				return -100;
			}

			// Setup logging and Sentry
			Local.OnLogError = (m) =>
			{
				SentrySdk.CaptureMessage(m, SentryLevel.Error);
			};

			Local.OnLogException = (m, ex) =>
			{
				SentrySdk.CaptureException(ex);
			};

			// Initialize licensing
			int license_check = CheckLicense();

			Local.log($"S-ARGAME Editor, version={GetVersionDescriptive()}, license_check={license_check}, root_folder={Local.RootFolder}");

			SARGAME = new SARGAME();
			int load_ret = SARGAME.Load();

			if (load_ret != 1)
			{
				// problems loading
				SentrySdk.CaptureMessage($"SARGAME.Load() ret={load_ret}", SentryLevel.Error);

				return -101;
			}

			// Start remote license validation
			ValidateLicenseOnServer();

			// if we have a license, log user
			string email = GetUserEmail();
			if (email != null)
				SentrySdk.ConfigureScope(scope =>
				{
					scope.User = new SentryUser
					{
						Email = email
					};
				});

			return 1;
		}

		internal void ResetLicenseInfo()
		{
			Avalonia.Threading.Dispatcher.UIThread.Invoke(() =>
			{
				MainWindowUI.EditedLicenseSerial = "";
				IsLicensed = false;
				LicenseCheck = -1000;
				IsTrial = false;
				IsTrialExpired = false;
				IsTimeLicense = false;
				IsTimeLicenseExpired = false;
				MainWindowUI.Instance.RaisePropertyChanged(nameof(MainWindowUI.Instance.IsLicensed));
				MainWindowUI.Instance.RaisePropertyChanged(nameof(MainWindowUI.Instance.IsTrial));
				MainWindowUI.Instance.RaisePropertyChanged(nameof(MainWindowUI.Instance.IsTrialExpired));
			});
		}

		// RETURN VALUES:
		// -100: cannot load license or no license
		// -101: exception loading license (filesystem?)
		// -102: exception checking license (filesystem?)

		// LocalAppConfig.CheckLicense()
		// 1: ok, 
		//-1: failed for HC or exception
		//-2: failed for date expired
		//-3: failed for instances lock
		//-4: failed for uses lock
		//-5: failed for everlasting license
		//-6: failed for invalid DEV or non loaded
		//-7: failed for blacklisted hid or serial
		//-8: failed for unknown expired license (usually old trial licenses, or deactivated license system)
		//-9: failed for expired trial
		internal int CheckLicense(bool dump=false)
		{
			void _refresh_license_ui(int license_check_ret)
			{
				Avalonia.Threading.Dispatcher.UIThread.Invoke(() =>
				{
					MainWindowUI.EditedLicenseSerial = LicenseInfo!=null ? (LicenseInfo.IsTrial() ? "" : LicenseInfo.Serial) : "";
					IsLicensed = license_check_ret > 0;
					LicenseCheck = license_check_ret;
					IsTimeLicense = LicenseInfo != null ? LicenseInfo.HasExpirationDate() : false;
					IsTimeLicenseExpired = LicenseInfo != null ? (!LicenseInfo.IsTrial() && LicenseInfo.HasExpirationDateExpired()): false;
					IsTrial = LicenseInfo != null ? LicenseInfo.IsTrial() : false;
					IsTrialExpired = LicenseInfo != null ? LicenseInfo.IsTrialExpired() : false;
					MainWindowUI.Instance.RaisePropertyChanged(nameof(MainWindowUI.Instance.IsLicensed));
					MainWindowUI.Instance.RaisePropertyChanged(nameof(MainWindowUI.Instance.IsTrial));
					MainWindowUI.Instance.RaisePropertyChanged(nameof(MainWindowUI.Instance.IsTrialExpired));
				});
			}


			// Note: Local here is valid

			// Instrument license
			try
			{
				int license_ret = LicenseActivatorLib.LoadLicense();

				if (license_ret <= 0)
				{
					// no license file, let's clear thing on the client size
					// NOTE: we need a restart in order to have it reload information
					ResetLicenseInfo();

					_refresh_license_ui(-100);

					return -100;
				}
			}
			catch(Exception ex)
			{
				Local.logException("App.CheckLicense() = -101", ex);

				ResetLicenseInfo();

				_refresh_license_ui(-101);

				return -101;
			}

			int license_check_ret = 0;

			LicenseInfo = new ReactorLicenseInfo();

			if (dump)
			{
				Local.log($"{LicenseActivatorLib.DumpLicenseInformation()}");
			}

			try
			{
				// Generalized license check with reporting
				// 1: ok, 
				//-1: failed for HC or exception
				//-2: failed for date expired
				//-3: failed for instances lock
				//-4: failed for uses lock
				//-5: failed for everlasting license
				//-6: failed for invalid DEV or non loaded
				//-7: failed for blacklisted hid or serial
				//-8: failed for unknown expired license (usually old trial licenses, or deactivated license system)

#if DEBUG
				license_check_ret = Local.CheckLicense(debug: true, use_legacy_hid: true);
#else
				license_check_ret = Local.CheckLicense(use_legacy_hid: true);
#endif
				Local.log($"Licensing: check={license_check_ret}");
			}
			catch (Exception ex)
			{
				Local.logException("App.CheckLicense() = -102", ex);
				
				_refresh_license_ui(license_check_ret);
				
				return -102;
			}

			_refresh_license_ui(license_check_ret);

			// reporting
			if (license_check_ret <= 0)
			{
				try
				{
					Local.logError($"CheckLicense failed, serial={LicenseInfo.Serial} loaded={LicenseInfo.IsLicenseLoaded} machine.HID={LicenseInfo.GetLegacyHID()} license.HID={LicenseInfo.DeviceData.LegacyHID}");
				}
				catch { }
			}

			return license_check_ret;
		}
		
		// async validates this license, in order to discover if it must be updated or it is iligimate
		// TODO: just a stub
		internal void ValidateLicenseOnServer()
		{
			if (string.IsNullOrEmpty(MainWindowUI.Instance.EditedLicenseSerial))
				return;

			Task.Run(async () =>
			{
				try
				{
					// try to get also unityuniqueid
					var unitytool_data = await SARGAME.Instance.GetLastUnityToolDataOrInvoke();

					var match = LicenseActivatorLib.ValidateLicense(MainWindowUI.Instance.EditedLicenseSerial,
						unity_uniqueid: unitytool_data?.systeminfo?.deviceUniqueIdentifier,
						use_legacy_hid: true);

					SARGAME.App.log($"ValidateLicenseOnServer: {match}");

					// TEST: a failed match results in removing the LOCAL license files, license will be asked again next run
					if (match < -2)
					{
						// TODO: exclude business?..
						var is_business = Local.CheckLicenseFeature("business");

						/*
						LicenseActivatorLib.RemoveLocalLicense();
						SARGAME.App.log("Local license has been removed");
						*/
					}
				}
				catch(Exception ex)
				{
					SARGAME.App.logException("ValidateLicenseOnServer", ex);
				}
			});
		}

		internal Version GetVersion()
		{
			System.Reflection.Assembly assembly = System.Reflection.Assembly.GetEntryAssembly();
			return assembly.GetName().Version;
		}

		internal string GetVersionDescriptive()
		{
			var version = GetVersion().ToString();
			var os = OperatingSystem.IsWindows() ? "Windows" : (OperatingSystem.IsMacOS() ? "Mac" : "<unknown>");

			var expired = IsTrialExpired ? " - TRIAL EXPIRED" : (IsTrial ? " - TRIAL" : (IsTimeLicenseExpired ? " - LICENSE EXPIRED" : ""));
#if BETA_RELEASE
		return $"{version} - {os} - BETA{expired}";
#else
			return $"{version} - {os}{expired}";
#endif

			return $"{version} - {os}";
			//System.Diagnostics.FileVersionInfo fvi = System.Diagnostics.FileVersionInfo.GetVersionInfo(assembly.Location);
			//return fvi.FileVersion;
		}

		internal string GetLicenseDescription()
		{
			if (Local == null)
				return string.Empty;

			if (MainWindowUI.Instance.LicenseNeedRestart)
			{
				return "Please restart the Editor to refresh the license";
			}

			var sb = new StringBuilder();

			if (IsTrialExpired)
			{
				sb.AppendLine($"License: Trial Expired");
			}
			else if (IsTrial)
			{
				// WORKAROUND: it seems trial on Mac do not work... they give 0 as current day?
				if (LicenseInfo.getTrialDay() == 0)
					sb.AppendLine($"License: Trial");
				else
					sb.AppendLine($"License: Trial (day {LicenseInfo.getTrialDay()} of {LicenseInfo.getTrialDays()})");
			}
			else if (IsTimeLicenseExpired)
			{
				sb.AppendLine($"License: Expired on {LicenseInfo.LicenseConfiguration.date_expire.ToShortDateString()}");
			}
			else
			{
				// any other case

				var license_check_ret = Local.CheckLicense(use_legacy_hid: true);

				string license_type = "HOME";

				if (Local.CheckLicenseFeature("business"))
					license_type = "BUSINESS";
				else if (Local.CheckLicenseFeature("premium"))
					license_type = "PREMIUM";
				else if (Local.CheckLicenseFeature("home"))
					license_type = "HOME";

				if (Local.IsLicensed() && license_check_ret > 0)
				{
					sb.AppendLine($"License: {license_type}\n");

					if (LicenseInfo.IsTrial())
					{
						var unit = LicenseInfo.IsTrialMinutes() ? "minutes" : "days";

						if (LicenseInfo.IsTrialExpired())
							sb.AppendLine($"Type: Trial (expired)\n");
						else
							sb.AppendLine($"Type: Trial ({LicenseInfo.getRemainingTrialDays()} {unit} remaining)\n");
					}
					else if (LicenseInfo.HasExpirationDate())
					{
						// TODO: subscription?
						sb.AppendLine($"Type: Date (expires on {LicenseInfo.LicenseConfiguration.date_expire.ToShortDateString()})\n");
					}


					sb.AppendLine($"User: {App.LicenseInfo.CustomerDescription}");

					// Features
					sb.AppendLine();

					// TODO: this has to be better engineered
					List<string> features = new();

					// New licenses
					if (Local.CheckLicenseFeature("home"))
						features.Add("HOME");
					if (Local.CheckLicenseFeature("premium"))
						features.Add("PREMIUM");
					if (Local.CheckLicenseFeature("business"))
						features.Add("BUSINESS");

					// StarterPack
					if (Local.CheckLicenseFeature("starterpack") || Local.CheckLicenseFeature("starterpack/*"))
						features.Add("STARTERPACK");
					if (Local.CheckLicenseFeature("starterpack/walleroids"))
						features.Add("Walleroids");
					if (Local.CheckLicenseFeature("starterpack/ponkanoid"))
						features.Add("Ponkanoid");
					if (Local.CheckLicenseFeature("starterpack/neonraiders"))
						features.Add("NeonRaiders");
					if (Local.CheckLicenseFeature("starterpack/tablerace"))
						features.Add("TableRace");

					// HolidayXP
					if (Local.CheckLicenseFeature("holidayxp/*"))
						features.Add("HOLIDAY XP");
					if (Local.CheckLicenseFeature("holidayxp/snowballfrenzy"))
						features.Add("SnowballFrenzy");
					if (Local.CheckLicenseFeature("holidayxp/holidayxp1"))
						features.Add("HolidayXP1");

					// 80sPack
					if (Local.CheckLicenseFeature("80pack/*"))
						features.Add("80s PACK");
					if (Local.CheckLicenseFeature("80pack/snakepit"))
						features.Add("SnakePit");
					if (Local.CheckLicenseFeature("80pack/superpop"))
						features.Add("SuperPop");
					if (Local.CheckLicenseFeature("80pack/bubblefi"))
						features.Add("BubbleFi");
					if (Local.CheckLicenseFeature("80pack/bitbomber"))
						features.Add("BitBomber");
					if (Local.CheckLicenseFeature("80pack/footwall"))
						features.Add("FootWall");

					// 90sPack
					if (Local.CheckLicenseFeature("90pack/*"))
						features.Add("90s PACK");

					// InteractiveWall
					if (Local.CheckLicenseFeature("interactivewall/*"))
						features.Add("INTERACTIVE WALL");

					// KinetikPack
					if (Local.CheckLicenseFeature("kinetikpack/*"))
						features.Add("KINETIK PACK");
					if (Local.CheckLicenseFeature("kinetikpack/kinetikplayground"))
						features.Add("KinetikPlayground");

					// RoadPack
					if (Local.CheckLicenseFeature("roadpack/*"))
						features.Add("ROAD PACK");

					// TabletopStrategy
					if (Local.CheckLicenseFeature("tabletopstrategypack/*"))
						features.Add("TABLETOP PACK");

					// HalloweenXP
					if (Local.CheckLicenseFeature("halloweenxp/*"))
						features.Add("HALLOWEEN XP");
					if (Local.CheckLicenseFeature("halloweenxp/pumpshoot"))
						features.Add("PumpShoot");

					// DanceXP
					if (Local.CheckLicenseFeature("dancexp/*"))
						features.Add("DANCE XP");

					sb.AppendLine($"Features: {string.Join(", ", features)}");
				}
				else
				{
					sb.AppendLine("Unlicensed");
				}
			}

			return sb.ToString();
		}

		internal string GetUserEmail()
		{
			if (LicenseInfo == null)
				return null;

			if (!string.IsNullOrEmpty(LicenseInfo.OrderData.customer_email))
				return LicenseInfo.OrderData.customer_email;
			else
				return null;
		}

		internal async void AboutMenuItem_OnClick(object sender, RoutedEventArgs args)
		{
			var about = $"S-ARGAME Editor\n(c) 2023 TABULA Natural Interfaces Srl\n\nVersion {GetVersionDescriptive()}\nLicense {GetLicenseDescription()}";

			await ProgressDialog.ShowMessageAsync(about, "OK");
		}

		public override void OnFrameworkInitializationCompleted()
		{
			if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
			{
				desktop.MainWindow = new MainWindow();
				MainWindow = desktop.MainWindow;
			}
			else if (ApplicationLifetime is ISingleViewApplicationLifetime singleViewPlatform)
			{
				var w = new MainView();
				singleViewPlatform.MainView = w;
				MainView = w;

				// NOTE: should MainView initialization before calling Application_Startup on mobile?

				//FileSystem.Configure(MainView);
			}

			base.OnFrameworkInitializationCompleted();
		}

		#region INotifyPropertyChanged

		protected void RaisePropertyChanged(PropertyChangedEventArgs args)
		{
			((IReactiveObject)this).RaisePropertyChanged(args);
		}

		protected void RaisePropertyChanged(string name)
		{
			((IReactiveObject)this).RaisePropertyChanged(name);
		}

		#endregion

		public async Task OnStartup()
		{
			Logger.DefaultLog.WriteLine("Editor Started");

			int initialize_ret = InitializeLocalAppAndLicense();

			if (initialize_ret != 1)
			{
				var error_message = $"A file-system permission problem is inhibiting the initialization.\nPlease report the <NAME_EMAIL>\n\nError: {initialize_ret}";

				Avalonia.Threading.Dispatcher.UIThread.Invoke(async () =>
				{
					await ProgressDialog.ShowMessageAsync(error_message, "OK");

					MainWindow.Close();
				});

				return;
			}

			// Setup global Json settings to deserialize without assembly name
			JsonConvert.DefaultSettings = () => SARGAME.JsonSettings;

			// CONFIG: refactor to be loaded from settings
			if (!SARGAME.App.ReadSetting("editor_config", out Config))
			{
				Config = new Config();
			}

			/*
			//if (!RuntimeInformation.RuntimeIdentifier.Contains("android"))
			{
				// Loads client config
				if (!File.Exists("config.json"))
				{
					Config = new Config();
					File.WriteAllText("config.json", JsonConvert.SerializeObject(Config));
				}
				else
				{
					Config = JsonConvert.DeserializeObject<Config>(File.ReadAllText("config.json"));
				}
			}
			*/

			// Create the client 
			Client = new SharedObjectMap_Client<Model, ModelView, PMCore_Client>();

#if DEBUG
			// Client.onLog += (s) => Debug.WriteLine(s);
#endif

			Client.OnConnectionChanged = (connected) =>
			{
				Avalonia.Threading.Dispatcher.UIThread.Invoke(() => OnConnectionChanged(connected));
			};

			//Tabula.SharedObjectMap.SharedObjectMap.IsCommitEnabled = false;

			// Register model changes to create visual views
			Tabula.SharedObjectMap.SharedObjectMap.notifyObjectAdded = OnModelAdded;
			Tabula.SharedObjectMap.SharedObjectMap.notifyObjectRemoved = OnModelRemoved;

			// General check task
			_check_task_cts = new CancellationTokenSource();
			Task.Run(() => CheckTask(_check_task_cts.Token));			

			// initial store check after 5 secs

			// await Task.Delay(5000);

			ProgressDialog msgbox;

#pragma warning disable CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
			Avalonia.Threading.Dispatcher.UIThread.Invoke(async () =>
			{
				msgbox = ProgressDialog.ShowMessage(
						message: "Initializing...",						
						progress: true);

				await CheckStoreUpdates();

				msgbox?.Close();


				Tabula.PWGClient.MainWindow.Instance.DataContext = MainWindowUI;

				MainWindowUI.RaiseAllPropertyChanged();

				// Ask for license activation the first time
				if (MainView.Instance != null)
					MainView.Instance.AskLicenseActivation(app_startup: true);

				// Sparkle Updater
#if !BETA_RELEASE
				CheckEditorUpdates();
#endif				
			});
#pragma warning restore CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed


		}

		public static async Task RestartAsync()
		{
			// Get the current process executable path
			var executablePath = Process.GetCurrentProcess().MainModule?.FileName;
			if (string.IsNullOrEmpty(executablePath))
			{
				throw new InvalidOperationException("Unable to determine the executable path.");
			}

			// Get the command line arguments
			var args = string.Join(" ", Environment.GetCommandLineArgs().Skip(1).Select(arg => $"\"{arg}\""));

			// Closes external windows (NetSparkle)
			if (NetSparkleUpdater.UI.Avalonia.UpdateAvailableWindow.Instance != null)
				NetSparkleUpdater.UI.Avalonia.UpdateAvailableWindow.Instance.Close();

			// Close the current application
			Tabula.PWGClient.MainWindow.Instance.Close();

			// Wait for a short period to ensure the application has exited
			// await Task.Delay(1000);

			// Start a new instance of the executable
			Process.Start(new ProcessStartInfo
			{
				FileName = executablePath,
				Arguments = args,
				UseShellExecute = false
			});
		}

		#region Sparkle Updater

		public void CheckEditorUpdates()
		{
			InitSparkleUpdater();

			// Auto check for Editor updates at startup 
			CheckForUpdatesWhenSparkleHasStarted();
		}
		
		// General checks
		private CancellationTokenSource? _check_task_cts;
		private async Task CheckTask(CancellationToken token)
		{
			bool macos_update_available_proposed = false;

			Stopwatch sw_one_sec = new Stopwatch();

			sw_one_sec.Restart();

			while (!token.IsCancellationRequested)
			{
				bool proc_running = false;
				PackageModule running_module = null;

				try
				{					
					if (SARGAME.Instance != null)
					{
						var pinfo = SARGAME.Instance.ReadProcessInfo();
						if (pinfo.processinfo != null)
						{
							proc_running = pinfo.processinfo.IsProcessAlive();
							if (proc_running)
							{
								// try to find the installed module
								if (!string.IsNullOrEmpty(pinfo.processinfo.module_name))
									running_module = SARGAME.Instance.GetModule(pinfo.processinfo.module_name);

								// special case for developers
								if (running_module==null && pinfo.processinfo.process_name == "Unity")
									running_module = SARGAME.Instance.GetUnityModule(pinfo.processinfo);
							}
						}
					}
				}
				catch (Exception ex)
				{
					// not finding the process will throw
				}

				// start the task to refresh authcodes and get players stats
				if (sw_one_sec.ElapsedMilliseconds >= 1000)
				{
					try
					{
						App.RefreshAuthcodes();

						App.GetPlayerStatistics();
					}
					catch { }

					sw_one_sec.Restart();
				}


				// Check the running module
				Avalonia.Threading.Dispatcher.UIThread.Invoke(() =>
				{
					MainWindowUI.IsModuleProcessRunning = proc_running;
					MainWindowUI.RunningModule = running_module;

					// license description
					MainWindowUI.RaisePropertyChanged(nameof(MainWindowUI.LicenseDescription));

					// application focus notice
					MainWindowUI.RaisePropertyChanged(nameof(MainWindowUI.IsApplicationInFocus));
				});

				await Task.Delay(500);
			}
		}

		private void InitSparkleUpdater()
		{
#if DEBUG
			return;
#endif

			SARGAME.App.log($"AppCastUrl = {AppCastUrl}");

			_sparkle = new SparkleUpdater(
				AppCastUrl, // link to your app cast file
				new Ed25519Checker(SecurityMode.Unsafe)
			)
			{
				UIFactory = new NetSparkleUpdater.UI.Avalonia.UIFactory(null)
				{
					UpdateWindowGridBackgroundBrush = Avalonia.Media.Brushes.Black,
					HideSkipButton = true
				}, // or null or choose some other UI factory or build your own!
				
				// Avalonia version doesn't support separate threads: https://github.com/AvaloniaUI/Avalonia/issues/3434#issuecomment-573446972
				ShowsUIOnMainThread = true,
				LogWriter = new NetSparkleSARGAMELogWriter(false), //new LogWriter(false)
				UseNotificationToast = false, // Avalonia version doesn't yet support notification toast messages
				RelaunchAfterUpdate = false
			};

			_sparkle.UpdateCheckStarted += (s) => SARGAME.App?.log("main: UpdateCheckStarted");
			_sparkle.UpdateDetected += (s, a) =>
			{
				// NOTE: link is not used at the moment
				SARGAME.App?.log($"main: UpdateDetected, latest_version={a.LatestVersion.Version} link={a.LatestVersion.DownloadLink}");
				// UpdateDownloadLink = a.LatestVersion.DownloadLink;
			};

			_sparkle.UpdateCheckFinished += (s, status) =>
			{
				// NOTE: status is not used at the moment
				SARGAME.App?.log($"main: UpdateCheckFinished, status={status}");
				// UpdateCheckStatus = status;
			};

			_sparkle.DownloadStarted += (item,path) => SARGAME.App?.log($"main: DownloadStarted, item={item.Version} path={path}");
			_sparkle.DownloadHadError += (item,path,ex) => SARGAME.App?.log($"main: DownloadHadError, item={item.Version} path={path} ex={ex}");
			_sparkle.DownloadedFileIsCorrupt += (item,path) => SARGAME.App?.log($"main: DownloadedFileIsCorrupt, item={item.Version} path={path}");
			_sparkle.DownloadFinished += (item, path) => SARGAME.App?.log($"main: DownloadFinished, item={item.Version} path={path}");
			_sparkle.DownloadCanceled += (item, path) => SARGAME.App?.log($"main: DownloadCanceled, item={item.Version} path={path}");

			_sparkle?.StartLoop(true);
		}

		private void CheckForUpdatesWhenSparkleHasStarted()
		{
			Task.Run(async () =>
			{
				await Task.Delay(2000);
				while (_sparkle == null)
				{
					await Task.Delay(1000);
					System.Diagnostics.Debug.WriteLine($"Waiting for sparkle init...");
				}

                Avalonia.Threading.Dispatcher.UIThread.Post(async () => await _sparkle.CheckForUpdatesAtUserRequest(false));
			});
		}


		bool _is_checking_store_updates = false;
		public async Task CheckStoreUpdates()
		{
			if (_is_checking_store_updates)
				return;

			_is_checking_store_updates = true;

			// Refresh store and check for updates
			await Task.Run(async () =>
			{
				try
				{
					await SARGAME.Instance.GetStore();

					int pending_updates = 0;
					foreach (var p in SARGAME.Instance.Packages.ToArray())
					{
						var store_package = p.Package.StorePackage;
						if (store_package != null)
						{
							var ret = await store_package.CheckForStoreUpdatesUsingXML(editor_version: GetVersion(), notify_update: true);
							if (ret.url != null)
								pending_updates++;
						}
					}

					if (pending_updates>0)
						App.Instance.ShowNotification($"There are {pending_updates} package updates!");
				}
				catch(Exception ex)
				{
					SARGAME.App?.logException("CheckForStoreUpdates()", ex);
				}

				_is_checking_store_updates = false;
			});
		}

		#endregion

		#region Notifications

		public void ShowNotification(string message)
		{
			async Task _show()
			{
				MainWindowUI.NotificationOpacity = 0;

				for (double i = 0; i < 1; i += 0.05)
				{
					MainWindowUI.NotificationOpacity = i;
					await Task.Delay(10);
				}

				MainWindowUI.NotificationOpacity = 1;

				await Task.Delay(6000);

				for (double i = 1; i >= 0; i -= 0.05)
				{
					MainWindowUI.NotificationOpacity = i;
					await Task.Delay(20);
				}

				MainWindowUI.NotificationOpacity = 0;
			}

			Avalonia.Threading.Dispatcher.UIThread.Invoke(() =>
			{
				MainWindowUI.Instance.NotificationText = message;
				_show();
			});
        }

		/*
		private WindowNotificationManager notificationManager;
		public void ShowNotification(string title, string message)
		{
			if (notificationManager == null)
				notificationManager = new WindowNotificationManager(Tabula.PWGClient.MainWindow.Instance);

			try
			{
				var notif = new Avalonia.Controls.Notifications.Notification(title, message);
				notificationManager.Position = NotificationPosition.BottomRight;
				notificationManager.Show(notif);
			}
			catch (Exception exc)
			{
				
			}
		}
		*/

		#endregion

		public static async void MessageBox(string title, string message = "")
		{
			var msgbox = new MessageBox();

			await msgbox.ShowDialog(MainWindow);
		}


		#region Server Connection

		// Connect to PMCore server
		// Returns client info, which can also be an error code
		// -20: cancellation
		public async Task<int> ConnectToServer(CancellationToken main_timeout_token=default, int ms_internal_timeout=2000)
		{
			// Set Avalonia dispatcher
			// TODO: here??
			Tabula.SharedObjectMap.Dispatcher.Set(Avalonia.Threading.Dispatcher.UIThread, Thread.CurrentThread.ManagedThreadId);

			// pause rendering
			MainView.Instance.ToggleSceneControlsRendering(false);
			// TODO: should wait for end of last render
			await Task.Delay(500);

			var info = new ConnectionInfo();

			async Task<int> _connection_try()
			{
				int connection_result = 0;

				while (!main_timeout_token.IsCancellationRequested)
				{
					// create a cancellation token that timesout after 1 second
					// this allows to retry the whole connection more times, keeps sockets happy
					var cts_internal = new CancellationTokenSource(TimeSpan.FromMilliseconds(ms_internal_timeout));

					connection_result = await _connect_to_server(info, token: cts_internal.Token);  // was main_timeout_token
					if (connection_result > 0)
						return connection_result;

					// this is the global timeout
					if (main_timeout_token.IsCancellationRequested)
						return -20;
				}

				return connection_result;
			}

			if (Config.UseP2P)
			{
				ClientData p2p_client_data = null;

				Task.Run(() => StartClient("SharedModelRPC_Server_Net", async (client_data) => p2p_client_data = client_data));

				// TODO: timeout
				while (p2p_client_data == null)
					await Task.Delay(10);

				if (p2p_client_data.status == ClientData.State.Connected)
				{
					info.server_address = p2p_client_data.ip;
					info.rpc_port = p2p_client_data.port;

					return await _connection_try();
				}
				else
				{
					return -1;  // error
				}
			}
			else
			{
				info.server_address = string.IsNullOrEmpty(Config.ServerAddress) ? "127.0.0.1" : Config.ServerAddress;

				return await _connection_try();
			}
		}

		// RETURNS:
		// -1: error creating native client
		// -3: error checking connection
		// -10: already in progress
		// -11: exception
		// -30: protocol mismatch
		private async Task<int> _connect_to_server(ConnectionInfo info, CancellationToken token=default)
		{
			if (MainWindowUI.Instance.IsConnectingOrDisconnecting)
				return -10;

			MainWindowUI.Instance.IsConnectingOrDisconnecting = true;

			int connection_status = 0;

			try
			{

				connection_status = await App.Client.Connect(info, token: token);  //"************" "127.0.0.1" "tabula server ************"

				if (connection_status > 0)
				{
					// check protocol
					int server_protocol_version = await Client.Client.GetProtocolVersion();
					if (server_protocol_version != SARGAME.Version)
						return -30;

					// get icons first, so that visuals can be created

					// NOTE: now we load them from folder because it's faster, in a real client-server we should use GetIcons()
					// let's check if the process is Unity, in this case let's get it from network
					var pinfo = SARGAME.Instance.ReadProcessInfo();
					if (pinfo.processinfo!=null && pinfo.processinfo.process_name == "Unity")
					{
						await GetIcons();
					}
					else
					{
						// read icons directly from folder
						GetIconsFromPackageFolder();
					}

					// TEST: remove all PMViews to make sure!
					PMViewExt.DestroyAllPMViews();

					// gets model from server, and sets up the update server
					// Sets the Model in SARGAME so that during view creation the model is valorized (after core migration)
					await App.Client.ReceiveModel(beforeSetModel: (model) => SARGAME.Model = model);

					if (App.Client.Model != null)
					{
						SARGAME.View = App.Client.View;
						//MainWindowUI.View = App.Client.View;

						AfterModelLoaded();

						MainWindowUI.Instance.RefreshHierarchy();
					}
					else
					{
						// App.Client.ConnectedClientInfo would hold error
						// await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() => App.MessageBox("Error getting model"));
						App.Local.logError("App._connect_to_server: Error receiving model");
					}

					// Get Entities to create
					await MainWindowUI.Instance.GetEntitiesToCreate();
				}

			}
			catch(Exception ex)
			{
				App.Local.logException("_connect_to_server", ex);

				MainWindowUI.Instance.IsConnectingOrDisconnecting = false;

				Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() => MainWindowUI.RaisePropertyChanged(nameof(IsConnected)));

				return -11;
			}

			MainWindowUI.Instance.IsConnectingOrDisconnecting = false;

			Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() => MainWindowUI.RaisePropertyChanged(nameof(IsConnected)));

			return connection_status;
		}

		public async Task DisconnectFromServer()
		{
			// pause rendering
			MainView.Instance.ToggleSceneControlsRendering(false);
			// TODO: should wait for end of last render
			await Task.Delay(500);

			await Client?.Disconnect();			

			// TODO: clean scenes!
		}

		public async Task DisconnectAndStopModule()
		{
			// TEST: on Mac let's call API.Quit()
			if (OperatingSystem.IsMacOS())
			{
				await Client.Client.Quit(0);
			}
			else
			{
				if (IsConnected)
					await Instance.DisconnectFromServer();

				await Instance.StopModule();
			}
		}

		[SuppressPropertyChangedWarnings]
		public void OnConnectionChanged(bool connected)
		{
			if (connected)
			{
				MainWindowUI.IsConnected = true;
				MainWindowUI.Instance.IsConnectingOrDisconnecting = false;
			}
			else
			{
				// This is called before Model clear, stop rendering to avoid deadlocks in SKScene

				App.Client.OnModelCleared = () =>
				{
					Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() => MainWindowUI.RaisePropertyChanged(nameof(IsConnected)));
					App.Client.OnModelCleared = null;
				};

				// pause rendering
				MainView.Instance.ToggleSceneControlsRendering(false);
				// TODO: should wait for end of last render
				Task.Delay(500).Wait();

				SARGAME.View = null;
				//MainWindowUI.View = null;

				MainWindowUI.IsConnected = false;
				MainWindowUI.Instance.IsConnectingOrDisconnecting = false;

				// Reset some UI
				MainWindowUI.AuthCode = null;
				MainWindowUI.AdminAuthCode = null;

				// Go back to the Dashboard
				MainView.Instance.tab_dashboard.IsSelected = true;
				MainWindowUI.Instance.IsInspectorVisible = false;
				MainWindowUI.Instance.ResetRecentObjects();
			}
		}

		public async Task GetIcons()
		{
			// TODO: retrieves icon list, then icons to cache them
			string[] icons = await App.Client.Client.GetIconList();
			if (icons == null)
				return;

			byte[] icon = null;

			int last_index = 0;
			try
			{
				foreach (var i in icons)
				{
					last_index++;
					icon = await App.Client.Client.GetIcon(i);
					if (icon != null)
						Icons.TryAdd(i, icon);
				}
			}
			catch(Exception ex)
			{
				// KCP_PORTING: Cannot get Icon as a single message, too big for KCP
				throw new Exception($"Cannot GetIcon() name = {icons[last_index]}");
			}
		}

		public void GetIconsFromPackageFolder()
		{
			if (MainWindowUI.Instance.RunningModule == null)
				return;

			var icon_files = Directory.GetFiles(MainWindowUI.Instance.RunningModule.Package.IconsFolder, "*.png", SearchOption.TopDirectoryOnly);
			
			byte[] icon = null;
			string icon_name = "";

			try
			{
				foreach (var i in icon_files)
				{
					icon_name = Path.GetFileNameWithoutExtension(i);

					icon = File.ReadAllBytes(i);
					if (icon != null)
						Icons.TryAdd(icon_name, icon);
				}
			}
			catch (Exception ex)
			{				
				throw new Exception($"Cannot GetIcon() name = {icon_name} from package folder");
			}
		}

		public static byte[] GetIcon(string icon_name)
		{
			if (!string.IsNullOrEmpty(icon_name) && Icons.TryGetValue(icon_name, out byte[] data))
			{
				return data;
			}
			else
			{
				// _default icon must exist
				if (Icons.TryGetValue("_default", out byte[] default_data))
				{
					return default_data;
				}
				else
					return null;
			}
		}

		#region AuthCodes

		public static void RefreshAuthcodes()
		{
			if (App.IsConnected && App.Client != null && App.Client.Client != null)
			{
				Task.Run(async () =>
				{
					try
					{
						var authcodes = await App.Client.Client.GetAuthCodes();

						Avalonia.Threading.Dispatcher.UIThread.Invoke(() =>
						{
							MainWindowUI.Instance.AuthCode = authcodes[0];
							MainWindowUI.Instance.AdminAuthCode = authcodes[1];
						});
					}
					catch
					{
						MainWindowUI.Instance.AuthCode = null;
						MainWindowUI.Instance.AdminAuthCode = null;
					}
				});
			}
		}

		#endregion

		#region Player Stats

		public static void GetPlayerStatistics()
		{
			if (App.IsConnected && App.Client != null && App.Client.View!=null)
			{
				Task.Run(async () =>
				{
					try
					{
						var stats = await App.Client.Client.GetStatistics();
						
						
						Avalonia.Threading.Dispatcher.UIThread.Invoke(() =>
						{
							try
							{
								MainWindowUI.Instance.PlayersWaiting = NamedValue.GetInt(stats, "players_waiting");
								MainWindowUI.Instance.PlayersSetup = NamedValue.GetInt(stats, "players_setup");
								MainWindowUI.Instance.PlayersReady = NamedValue.GetInt(stats, "players_ready");
								MainWindowUI.Instance.PlayersPlaying = NamedValue.GetInt(stats, "players_playing");
							}
							catch {
								MainWindowUI.Instance.PlayersWaiting = -1;
								MainWindowUI.Instance.PlayersSetup = -1;
								MainWindowUI.Instance.PlayersReady = -1;
								MainWindowUI.Instance.PlayersPlaying = -1;
							}

							try
							{
								MainWindowUI.Instance.ServerConnections = NamedValue.GetInt(stats, "server_connections");
								MainWindowUI.Instance.ServerExceptions = NamedValue.GetInt(stats, "server_exceptions");
								MainWindowUI.Instance.ServerCalls = NamedValue.GetInt(stats, "server_calls");
								MainWindowUI.Instance.ServerMessagesSent = NamedValue.GetInt(stats, "server_messages_sent_count");
								MainWindowUI.Instance.ServerMessagesReceived = NamedValue.GetInt(stats, "server_messages_received_count");
							}
							catch {
								MainWindowUI.Instance.ServerConnections = -1;
								MainWindowUI.Instance.ServerExceptions = -1;
								MainWindowUI.Instance.ServerCalls = -1;
								MainWindowUI.Instance.ServerMessagesSent = -1;
								MainWindowUI.Instance.ServerMessagesReceived = -1;
							}
						});
						
					}
					catch
					{}

					// TODO: issue a single refresh of text string instead of DependsOn ?
				});
			}
		}

		#endregion

		#endregion


		#region Model Events

		public void AfterModelLoaded()
		{
			// disable the outputsurface region (only warp is active at the beginning)
			var region = PMViewExt.GetPMViewsOfType<PMOutputSurfaceRegionView>().FirstOrDefault();
			if (region != null)
				region.Visual.IsEnabled = false;
		}

		public void OnModelAdded(Tabula.SharedObjectMap.GuidObject o)
		{
			Avalonia.Threading.Dispatcher.UIThread.Invoke(() =>
			{

				IPMView view = null;

				// Keep scenes in sync, they will handle adding to scene / view dictionary
				switch (o)
				{
					case Model model:
						view = new PMModelView(o);	// Intercepting model changes?
						break;

					case ScreenMarker screen_marker:						
						view = new PMScreenMarkerView(ScreenEditor.Instance?.Scene, o, $"ScreenMarker_{o.__guid}");
						break;

					case ImageMarker image_marker:
						view = new PMImageMarkerView(ScreenEditor.Instance?.Scene, o, $"ImageMarker_{o.__guid}");
						break;

					case Cursor cursor:
						view = new PMCursorView(LevelEditor.Instance?.Scene, o, "Cursor");
						break;

					case Polygon polygon:
						view = new PMPolygonView(LevelEditor.Instance?.Scene, o, "Polygon");
						break;

					case Structure structure:
						view = new PMStructureView(LevelEditor.Instance?.Scene, o, $"Structure_{o.__guid}");
						MainWindowUI.Instance?.RefreshHierarchy();
						break;

					case OutputSurface output_surface:
						var view_os = new PMOutputSurfaceView(ScreenEditor.Instance?.Scene, o, $"OutputSurface_{o.__guid}");
						var view_or_region = new PMOutputSurfaceRegionView(ScreenEditor.Instance?.Scene, o, $"OutputSurfaceRegion_{o.__guid}");
						break;

					case StructureEffect structure_effect:
						// just catch, they are visualized in strutureview no need for pm wrappers
						break;

					case Entity entity:

						// do not process templates and invisible entities
						if (!entity.flags.HasFlag(Entity.Flags.HasVisual))
						{
							MainWindowUI.Instance?.RefreshHierarchy();	// FIXME: useful?
							break;
						}

						// NEW: intercept special entities
						switch(entity.type)
						{
							case "polyline":
								view = new PMPolyLineView(LevelEditor.Instance?.Scene, o, $"PolyLine_{o.__guid}");
								break;

							default:
								view = new PMEntityView(LevelEditor.Instance?.Scene, o, $"Entity_{o.__guid}");
								break;
						}
						
						MainWindowUI.Instance?.RefreshHierarchy();
						break;
				}

				//LevelEditor.Instance?.Scene.Refresh();
				//ScreenEditor.Instance?.Scene.Refresh();
			});
		}

		public void OnModelRemoved(Tabula.SharedObjectMap.GuidObject o)
		{
			Avalonia.Threading.Dispatcher.UIThread.Invoke(() =>
			{
				var views = PMViewExt.GetPMViewsFromModel(o);
				if (views != null)
					foreach (var v in views)
						v.Destroy();

				if (o is Structure || (o is Entity && o is not StructureEffect))
				{
					MainWindowUI.Instance?.RefreshHierarchy();
				}

				//LevelEditor.Instance?.Scene.Refresh();
				//ScreenEditor.Instance?.Scene.Refresh();
			});
		}

		#endregion

		#region Model Helpers

		public ScenarioEntry GetCurrentScenario()
		{
			if (App.Client == null || App.Client.Model == null)
				return null;

			if (string.IsNullOrEmpty(App.Client.Model.ScenarioId))
				return null;

			return SARGAME.Instance.GetScenario(App.Client.Model.ScenarioId);
		}

		#endregion


		#region Calibration

		// deprecated
		/*
		public async Task<bool> IsCalibrationImageChanged()
		{
			if (File.Exists(calibration_image_time_path))
			{
				var local_calibration_image_time = JsonConvert.DeserializeObject<DateTime>(File.ReadAllText(calibration_image_time_path));

				var server_calibration_image_time = await Client.Client.GetCalibrationImageTime();

				return (server_calibration_image_time > local_calibration_image_time);
			}
			else
			{
				return false;
			}
		}


		// deprecated
		public async Task<SKImage> GetCalibrationImage()
		{
			if (Client.View == null)
				return null;

			DateTime calibration_image_time = DateTime.MinValue;

			string calibration_image_path = Client.View.Screen.calibration_image.file;

			if (!await IsCalibrationImageChanged())
			{
				// return cached image
				if (File.Exists(calibration_image_path))
					return SKImage.FromEncodedData(File.ReadAllBytes(calibration_image_path));
			}


			try
			{
				
				//var transfer = await Client.Client.GetCalibrationImage();
				//if (transfer == null)
				//	throw new Exception();
				//await transfer.ReceiveFileStream(calibration_image_path, true);
				
			}
			catch (Exception ex)
			{
				Debug.WriteLine("Cannot get Calibration Image");
			}

			File.WriteAllText(calibration_image_time_path, JsonConvert.SerializeObject(Client.View.Screen.calibration_image.time));

			if (File.Exists(calibration_image_path))
				return SKImage.FromEncodedData(File.ReadAllBytes(calibration_image_path));
			else
				return null;
		}
		*/

		#endregion

		#region UI

		
		async void bt_entity_fieldgroup_header_pressed(object sender, Avalonia.Input.PointerPressedEventArgs args)
		{
			FieldGroup fg = (FieldGroup)(sender as Control).DataContext;
			fg.IsExpanded = !fg.IsExpanded;
		}

		async void bt_entity_tools_button(object sender, Avalonia.Input.PointerPressedEventArgs args)
		{
			EntityView ev = (EntityView)(sender as Control).DataContext;
			string operation = (sender as Control).Tag.ToString();

			// check tag?
			switch (operation)
			{
				case "lock": ev.IsLocked = !ev.IsLocked; break;
			}
			
		}

		// meta action to call a method from an Entity field
		async void bt_entity_field_button(object sender, RoutedEventArgs args)
		{
			ButtonFieldView field_view = (ButtonFieldView)(sender as Control).DataContext;
			EntityView entity_view = (EntityView)field_view.__Parent;

			// Action must be called with the know method CallMethod
			// using action or name depends if target is a GameEntity (handles it in OnFieldsUpdate) or a PMEntityWrapper (handles it as a GuidObjectCallable
			entity_view.CallMethod(string.IsNullOrEmpty(field_view.action) ? field_view.name : field_view.action, null);
		}

		async void bt_structure_effects_add_pressed(object sender, Avalonia.Input.PointerPressedEventArgs args)
		{
			var s = (StructureView)(sender as Control).DataContext;

			var created_structure_effect = await StructureEffectAddDialog.Show();

			if (created_structure_effect != null)
			{
				// since it is not a normal entity, provide the guid of the parent (will be added to effets)
				var created_effect = (StructureEffect) await PMEntityView.CreateEntityOnServer(created_structure_effect, parent_guid: s.__Guid, add_to_recent: false);

				// expand this for default
				if (created_effect != null)
				{
					var v = created_effect.getView();
					if (v!=null)
						v.IsExpanded = true;
				}
				else
				{
					await ProgressDialog.ShowMessageAsync("Error creating Effect");
				}

				return;
			}
		}

		async void bt_structureeffect_header_pressed(object sender, Avalonia.Input.PointerPressedEventArgs args)
		{
			var e = (StructureEffectView)(sender as Control).DataContext;
			e.IsExpanded = !e.IsExpanded;
		}

		async void bt_structure_effect_delete_pressed(object sender, Avalonia.Input.PointerPressedEventArgs args)
		{
			var sev = (StructureEffectView)(sender as Control).DataContext;

			var structure = sev.GetParent() as StructureView;

			var index = structure.effects.IndexOf(sev);
			if (index != -1)
				structure.effects_Remove(index);
		}

		public static FilePickerFileType CustomFiles(string title, string[] file_types)
		{
			return new FilePickerFileType(title)
			{
				Patterns = file_types
			};
		}

		async void bt_entity_file_field_button(object sender, RoutedEventArgs args)
		{
			FileFieldView field_view = (FileFieldView)(sender as Control).DataContext;

			var file_filter = new List<FilePickerFileType>();

			if (field_view.file_types == null || field_view.file_types.Count == 0)
				file_filter = new List<FilePickerFileType>() { FilePickerFileTypes.ImageAll };
			else
				file_filter = new List<FilePickerFileType>()
				{
					new FilePickerFileType(field_view.title)
					{
						Patterns = field_view.Model.file_types
					}
				};

			var files_picked = await TopLevel.GetTopLevel(MainView.Instance).StorageProvider.OpenFilePickerAsync(new Avalonia.Platform.Storage.FilePickerOpenOptions()
			{
				Title = field_view.title,
				FileTypeFilter = file_filter,
				AllowMultiple = false
			});

			if (files_picked != null && files_picked.Count > 0)
			{
				field_view.value = files_picked[0].TryGetLocalPath();
			}
			else
			{
				field_view.value = "";
			}
		}

		#endregion

		#region Player Start and functions

		/*
		public async Task<bool> StartGame(string game, double timeout_seconds = 10)
		{
			// check game is not already running
			Process[] procs = Process.GetProcessesByName("S-ARKADE_Pack1"); // Path.GetFileNameWithoutExtension(CurrentModel.SensingProcessFilename));
			if (procs.Length > 0)
			{
				Logger.DefaultLog.WriteLine("S-ARKADE_Editor:: Fame already running");
				return false;
			}

			return await Task.Run(async () =>
				{
					DateTime time_start = DateTime.Now;

					try
					{
						string game_path = Path.Combine(Config.GameFolder, "S-ARKADE_Pack1.exe");
						string game_args = $"-screen-fullscreen 1 -monitor 2 -screen-width 1920 -screen-height 1080 -scene {game}";

						Logger.DefaultLog.WriteLine($"{game_path} {game_args}");

						ProcessStartInfo info = new ProcessStartInfo();
						info.WorkingDirectory = Config.GameFolder;
						info.FileName = game_path;
						info.Arguments = game_args;

						process_game = Process.Start(info);
						process_game.EnableRaisingEvents = true;
					}
					catch (Exception ex)
					{
						process_game = null;
						Logger.DefaultLog.logException("S-ARKADE_Editor: StartGame()", ex);
						return false;
					}

					// wait the window handle for a maximum time 
					try
					{
						while (process_game.MainWindowHandle == IntPtr.Zero)
						{
							await Task.Delay(100);

							if ((DateTime.Now - time_start).TotalSeconds > timeout_seconds)
							{
								Logger.DefaultLog.WriteLine("S-ARKADE_Editor: Start game timed out!");
								return false;
							}
						}
					}
					catch (Exception ex)
					{
						Logger.DefaultLog.logException("S-ARKADE_Editor:StartGame()", ex);
						return false;
					}

					//fx.IsRunning = true;
					//RunningEffect = fx;

					// Position the window on the right screen, and focus on it
					
					//if (!fx.TestWindow)
					//{
					//	SetWindowPos(process_game.MainWindowHandle, 0, fx.WindowLeft, fx.WindowTop, 0, 0, SWP_NOZORDER | SWP_NOSIZE);
					//	SetFocusReliable(process_game.MainWindowHandle);
					//}
					

					process_game.Exited += (s, e) =>
					{
						// RunningEffect = null;
						// fx.IsRunning = false;
						int exitcode = process_game.ExitCode;

						Logger.DefaultLog.WriteLine("S-ARKADE_Editor: Game exited with code=" + exitcode);

						process_game = null;

						Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
						{
							MainWindowUI.RaisePropertyChanged(nameof(MainWindowUI.IsGameRunning));
							MainWindowUI.RaisePropertyChanged(nameof(MainWindowUI.ShouldShowGamesToStart));
						});

						//onEffectExit?.Invoke();
					};

					Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
					{
						MainWindowUI.RaisePropertyChanged(nameof(MainWindowUI.IsGameRunning));
						MainWindowUI.RaisePropertyChanged(nameof(MainWindowUI.ShouldShowGamesToStart));
					});

					return true;
				});
		}
		*/

		public async Task<bool> StopModule(int wait_seconds = 10)
		{
			var pinfo = SARGAME.Instance.ReadProcessInfo();

			if (pinfo.processinfo == null)
			{
				SARGAME.App.log("StopModule(): cannot get process info");
				return false;
			}

			var process_module = pinfo.processinfo.GetProcess();
			if (process_module == null)
			{
				SARGAME.App.log("StopModule(): cannot get process module");
				return false;
			}

			if (OperatingSystem.IsWindows())
				process_module.CloseMainWindow();
			else if (OperatingSystem.IsMacOS())
			{
				SARGAME.App.log($"StopModule(): (Mac) killing process {process_module.Id}");
				process_module.Kill();
			}

			var cts = new CancellationTokenSource(TimeSpan.FromSeconds(wait_seconds));

			await ProgressDialog.ShowAsync(new ProgressDialogView()
			{
				Message = "Stopping...",
				IsProgressIndeterminate = true,
				ShowCancelButton = true,
				CancellationToken = cts.Token,
				Action = async (token) =>
				{
					// wait until it has exited
					await process_module.WaitForExitAsync(token);
				},
				CloseOnTaskFinish = true,
				OnTaskCanceled = () =>
				{
					SARGAME.App.logError("S-ARKADE_Editor::StopGame() timeout");					
				}
			}, identifier: "dialog");



			/*

			try
			{
				var cts = new CancellationTokenSource(TimeSpan.FromSeconds(wait_seconds));

				// wait until it has exited
				await process_module.WaitForExitAsync(cts.Token);
			}
			catch (OperationCanceledException ex)
			{
				SARGAME.App.logException("S-ARKADE_Editor::StopGame() timeout", ex);
				return false;
			}
			catch (Exception ex)
			{
				SARGAME.App.logException("S-ARKADE_Editor::StopGame()", ex);
				return false;
			}
			*/

			return true;
		}

		public async Task CheckProtocolVersion()
		{
			await SharedObjectMap.Dispatcher.DispatchAsync(async () =>
			{
				// Check the protocol version is OK
				if (MainWindowUI.Instance.View.Version != SARGAME.Version)
				{
					string error = "";
					if (MainWindowUI.Instance.View.Version < SARGAME.Version)
						error = "Version mismatch.\nPlease update the Package";
					else
						error = "Version mismatch.\nPlease update the Editor";


					await ProgressDialog.ShowMessageAsync(error);

					App.Instance.DisconnectFromServer();
				}
			});
		}


		// Uncommenting makes it not compile??
		/*
		[DllImport("user32.dll")]
		public static extern bool ShowWindow(int hWnd, int nCmdShow);

		[DllImport("kernel32.dll")]
		static extern int GetCurrentThreadId();

		[DllImport("user32.dll")]
		static extern bool AttachThreadInput(int idAttach, int idAttachTo, bool fAttach);

		[DllImport("user32.dll")]
		public static extern int GetWindowThreadProcessId(int hwnd, out int processId);

		[DllImport("user32.dll", SetLastError = true)]
		public static extern int SetActiveWindow(int hWnd);

		[DllImport("user32.dll")]
		public static extern IntPtr SetFocus(int hWnd);

		public static void setFocusReliable(int targetHwnd)
		{
			int currentThreadId = GetCurrentThreadId();
			int targetProcId = 0;
			int otherThreadId = GetWindowThreadProcessId(targetHwnd, out targetProcId);
			if (otherThreadId == 0) return;
			if (otherThreadId != currentThreadId)
			{
				AttachThreadInput(currentThreadId, otherThreadId, true);
			}

			SetActiveWindow(targetHwnd);
			SetFocus(targetHwnd);

			if (otherThreadId != currentThreadId)
			{
				AttachThreadInput(currentThreadId, otherThreadId, false);
			}
		}

		public const int SW_HIDE = 0;
		public const int SW_SHOW = 5;
		public const int SW_SHOWNORMAL = 1;
		public const int SW_SHOWMINIMIZED = 2;
		public const int SW_SHOWMAXIMIZED = 3;
		public const int SW_SHOWNOACTIVATE = 4;
		public const int SW_RESTORE = 9;
		public const int SW_SHOWDEFAULT = 10;

		// Will get the panel process (if existing) and send the mouse in the center of it
		public void CenterMouseOnModuleProcess()
		{
			var proc = SARGAME.Instance.CurrentModuleProcess;

			if (proc== null)
				return;

			if (proc.MainWindowHandle == IntPtr.Zero)
				return;

			{
				// Restore window
				ShowWindow(proc.MainWindowHandle.ToInt32(), SW_RESTORE);


				setFocusReliable(proc.MainWindowHandle.ToInt32());


				var rect = OSUtilities.getWindowRect(proc.MainWindowHandle);
				OSUtilities.setCursorPos(rect.Left + rect.Width / 2, rect.Top + rect.Height / 2);
			}
		}
		*/

		#endregion

		#region Utilities

		public static void OpenURL(string url)
		{
			if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
				Process.Start(new ProcessStartInfo(url.Replace("&", "^&")) { UseShellExecute = true });
			else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
				Process.Start("xdg-open", url);
			else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
				Process.Start("open", url);
		}

		#endregion


	}

}
